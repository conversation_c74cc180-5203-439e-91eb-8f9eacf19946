<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// 启动Laravel应用
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 简化版缺失记录分析 ===" . PHP_EOL;
echo "执行时间: " . date('Y-m-d H:i:s') . PHP_EOL . PHP_EOL;

try {
    // 1. 基础统计
    echo "1. 基础数据统计:" . PHP_EOL;
    $oracleCount = DB::connection('oracle')->table('BMBA_T')->where('BMBAENT', 40)->count();
    $mysqlCount = DB::table('BMBA_T')->count();
    $difference = $oracleCount - $mysqlCount;
    
    echo "   Oracle记录数: " . number_format($oracleCount) . PHP_EOL;
    echo "   MySQL记录数: " . number_format($mysqlCount) . PHP_EOL;
    echo "   缺失记录数: " . number_format($difference) . PHP_EOL . PHP_EOL;
    
    // 2. 随机抽样检查缺失记录
    echo "2. 随机抽样检查缺失记录:" . PHP_EOL;
    
    // 从Oracle随机获取100条记录
    $oracleRecords = DB::connection('oracle')
        ->table('BMBA_T')
        ->where('BMBAENT', 40)
        ->inRandomOrder()
        ->limit(100)
        ->get(['BMBAENT', 'BMBASITE', 'BMBA001', 'BMBA003', 'BMBA004']);
    
    echo "   获取Oracle随机样本: " . $oracleRecords->count() . " 条" . PHP_EOL;
    
    $missingInMysql = 0;
    $missingRecords = [];
    
    foreach ($oracleRecords as $record) {
        $exists = DB::table('BMBA_T')
            ->where('BMBAENT', $record->bmbaent)
            ->where('BMBASITE', $record->bmbasite)
            ->where('BMBA001', $record->bmba001)
            ->where('BMBA003', $record->bmba003)
            ->where('BMBA004', $record->bmba004)
            ->exists();
        
        if (!$exists) {
            $missingInMysql++;
            $missingRecords[] = [
                'BMBAENT' => $record->bmbaent,
                'BMBASITE' => $record->bmbasite,
                'BMBA001' => $record->bmba001,
                'BMBA003' => $record->bmba003,
                'BMBA004' => $record->bmba004,
            ];
        }
    }
    
    echo "   样本中缺失记录: {$missingInMysql}/100" . PHP_EOL;
    $estimatedMissing = ($missingInMysql / 100) * $oracleCount;
    echo "   估算总缺失记录: " . number_format($estimatedMissing) . " 条" . PHP_EOL . PHP_EOL;
    
    // 3. 分析缺失记录的特征
    if ($missingInMysql > 0) {
        echo "3. 分析缺失记录特征:" . PHP_EOL;
        
        $patterns = [
            'BMBASITE' => [],
            'BMBA001_prefix' => [],
            'BMBA003_prefix' => [],
            'BMBA004_pattern' => []
        ];
        
        foreach ($missingRecords as $record) {
            // 统计BMBASITE分布
            $patterns['BMBASITE'][$record['BMBASITE']] = ($patterns['BMBASITE'][$record['BMBASITE']] ?? 0) + 1;
            
            // 统计BMBA001前缀
            $prefix001 = substr($record['BMBA001'], 0, 4);
            $patterns['BMBA001_prefix'][$prefix001] = ($patterns['BMBA001_prefix'][$prefix001] ?? 0) + 1;
            
            // 统计BMBA003前缀
            $prefix003 = substr($record['BMBA003'], 0, 4);
            $patterns['BMBA003_prefix'][$prefix003] = ($patterns['BMBA003_prefix'][$prefix003] ?? 0) + 1;
            
            // 统计BMBA004模式
            $pattern004 = strlen(trim($record['BMBA004'])) == 0 ? 'EMPTY' : 'NOT_EMPTY';
            $patterns['BMBA004_pattern'][$pattern004] = ($patterns['BMBA004_pattern'][$pattern004] ?? 0) + 1;
        }
        
        echo "   BMBASITE分布:" . PHP_EOL;
        arsort($patterns['BMBASITE']);
        foreach ($patterns['BMBASITE'] as $site => $count) {
            echo "     {$site}: {$count} 条" . PHP_EOL;
        }
        
        echo "   BMBA001前缀分布:" . PHP_EOL;
        arsort($patterns['BMBA001_prefix']);
        foreach ($patterns['BMBA001_prefix'] as $prefix => $count) {
            echo "     {$prefix}*: {$count} 条" . PHP_EOL;
        }
        
        echo "   BMBA003前缀分布:" . PHP_EOL;
        arsort($patterns['BMBA003_prefix']);
        foreach ($patterns['BMBA003_prefix'] as $prefix => $count) {
            echo "     {$prefix}*: {$count} 条" . PHP_EOL;
        }
        
        echo "   BMBA004模式:" . PHP_EOL;
        foreach ($patterns['BMBA004_pattern'] as $pattern => $count) {
            echo "     {$pattern}: {$count} 条" . PHP_EOL;
        }
        
        echo PHP_EOL;
        
        // 4. 尝试手动插入缺失记录样本
        echo "4. 尝试手动插入缺失记录样本:" . PHP_EOL;
        
        $testRecords = array_slice($missingRecords, 0, 3);
        $insertSuccess = 0;
        $insertFailed = 0;
        $failureReasons = [];
        
        foreach ($testRecords as $record) {
            $keyStr = "{$record['BMBAENT']}|{$record['BMBASITE']}|{$record['BMBA001']}|{$record['BMBA003']}|{$record['BMBA004']}";
            echo "   测试插入: " . $keyStr . PHP_EOL;
            
            try {
                // 从Oracle获取完整记录
                $oracleRecord = DB::connection('oracle')
                    ->table('BMBA_T')
                    ->where('BMBAENT', $record['BMBAENT'])
                    ->where('BMBASITE', $record['BMBASITE'])
                    ->where('BMBA001', $record['BMBA001'])
                    ->where('BMBA003', $record['BMBA003'])
                    ->where('BMBA004', $record['BMBA004'])
                    ->first();
                
                if ($oracleRecord) {
                    $recordArray = (array) $oracleRecord;
                    
                    // 移除Oracle特有字段
                    unset($recordArray['rnum']);
                    unset($recordArray['RNUM']);
                    unset($recordArray['rn']);
                    unset($recordArray['RN']);
                    
                    // 转换为大写
                    $upperCaseRecord = [];
                    foreach ($recordArray as $key => $value) {
                        $upperCaseRecord[strtoupper($key)] = $value;
                    }
                    
                    // 检查数据长度
                    $dataIssues = [];
                    foreach ($upperCaseRecord as $field => $value) {
                        if (is_string($value) && strlen($value) > 255) {
                            $dataIssues[] = "{$field}=" . strlen($value) . "字符";
                        }
                    }
                    
                    if (!empty($dataIssues)) {
                        echo "     ⚠️  数据长度问题: " . implode(', ', $dataIssues) . PHP_EOL;
                    }
                    
                    // 尝试插入
                    DB::table('BMBA_T')->insert($upperCaseRecord);
                    $insertSuccess++;
                    echo "     ✅ 插入成功" . PHP_EOL;
                    
                    // 立即删除以免影响后续测试
                    DB::table('BMBA_T')
                        ->where('BMBAENT', $record['BMBAENT'])
                        ->where('BMBASITE', $record['BMBASITE'])
                        ->where('BMBA001', $record['BMBA001'])
                        ->where('BMBA003', $record['BMBA003'])
                        ->where('BMBA004', $record['BMBA004'])
                        ->delete();
                    
                } else {
                    echo "     ❌ Oracle中未找到记录" . PHP_EOL;
                    $insertFailed++;
                }
                
            } catch (Exception $e) {
                $insertFailed++;
                $errorMsg = $e->getMessage();
                echo "     ❌ 插入失败: " . $errorMsg . PHP_EOL;
                
                // 分析错误类型
                if (strpos($errorMsg, 'Duplicate entry') !== false) {
                    $failureReasons['duplicate'] = ($failureReasons['duplicate'] ?? 0) + 1;
                } elseif (strpos($errorMsg, 'Data too long') !== false) {
                    $failureReasons['data_too_long'] = ($failureReasons['data_too_long'] ?? 0) + 1;
                } elseif (strpos($errorMsg, 'Incorrect') !== false) {
                    $failureReasons['data_type'] = ($failureReasons['data_type'] ?? 0) + 1;
                } elseif (strpos($errorMsg, 'cannot be null') !== false) {
                    $failureReasons['null_constraint'] = ($failureReasons['null_constraint'] ?? 0) + 1;
                } else {
                    $failureReasons['other'] = ($failureReasons['other'] ?? 0) + 1;
                }
            }
        }
        
        echo PHP_EOL . "   插入测试结果:" . PHP_EOL;
        echo "     成功: {$insertSuccess}/" . count($testRecords) . PHP_EOL;
        echo "     失败: {$insertFailed}/" . count($testRecords) . PHP_EOL;
        
        if (!empty($failureReasons)) {
            echo "   失败原因分析:" . PHP_EOL;
            foreach ($failureReasons as $reason => $count) {
                echo "     {$reason}: {$count} 条" . PHP_EOL;
            }
        }
    }
    
    echo PHP_EOL;
    
    // 5. 检查MySQL表结构
    echo "5. 检查MySQL表结构:" . PHP_EOL;
    
    $tableInfo = DB::select("SHOW CREATE TABLE BMBA_T");
    if (!empty($tableInfo)) {
        $createSql = $tableInfo[0]->{'Create Table'};
        
        // 检查字符集
        if (preg_match('/CHARSET=(\w+)/', $createSql, $matches)) {
            echo "   字符集: " . $matches[1] . PHP_EOL;
        }
        
        // 检查引擎
        if (preg_match('/ENGINE=(\w+)/', $createSql, $matches)) {
            echo "   存储引擎: " . $matches[1] . PHP_EOL;
        }
    }
    
    echo PHP_EOL;
    
    // 6. 总结和建议
    echo "6. 问题总结和建议:" . PHP_EOL;
    
    if ($missingInMysql > 0) {
        $missingRate = ($missingInMysql / 100) * 100;
        echo "   📊 缺失率: {$missingRate}%" . PHP_EOL;
        
        if ($insertSuccess > 0) {
            echo "   ✅ 手动插入测试成功，说明数据本身没有问题" . PHP_EOL;
            echo "   🔍 可能原因: 同步过程中的批次失败或事务回滚" . PHP_EOL;
            echo "   💡 建议: 重新执行全量同步应该能解决问题" . PHP_EOL;
        } else {
            echo "   ❌ 手动插入测试失败，存在数据质量问题" . PHP_EOL;
            echo "   🔍 需要进一步分析失败原因" . PHP_EOL;
        }
    } else {
        echo "   ✅ 随机抽样未发现缺失记录，可能是统计误差" . PHP_EOL;
    }

} catch (Exception $e) {
    echo "❌ 调查过程中发生错误: " . $e->getMessage() . PHP_EOL;
    echo "错误详情: " . $e->getTraceAsString() . PHP_EOL;
}

echo PHP_EOL . "=== 调查完成 ===" . PHP_EOL;
