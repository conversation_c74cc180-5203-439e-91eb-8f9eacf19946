<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

// 启动Laravel应用
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== 调查21,575条缺失记录的原因 ===" . PHP_EOL;
echo "执行时间: " . date('Y-m-d H:i:s') . PHP_EOL . PHP_EOL;

try {
    // 1. 基础统计
    echo "1. 基础数据统计:" . PHP_EOL;
    $oracleCount = DB::connection('oracle')->table('BMBA_T')->where('BMBAENT', 40)->count();
    $mysqlCount = DB::table('BMBA_T')->count();
    $difference = $oracleCount - $mysqlCount;
    
    echo "   Oracle记录数: " . number_format($oracleCount) . PHP_EOL;
    echo "   MySQL记录数: " . number_format($mysqlCount) . PHP_EOL;
    echo "   缺失记录数: " . number_format($difference) . PHP_EOL . PHP_EOL;
    
    // 2. 使用SQL查找缺失记录（更高效）
    echo "2. 使用SQL查找缺失记录:" . PHP_EOL;

    // 使用LEFT JOIN找出Oracle中存在但MySQL中不存在的记录
    echo "   正在查找缺失记录..." . PHP_EOL;

    // 创建临时表来存储Oracle主键
    DB::statement("DROP TEMPORARY TABLE IF EXISTS temp_oracle_keys");
    DB::statement("
        CREATE TEMPORARY TABLE temp_oracle_keys (
            BMBAENT VARCHAR(10),
            BMBASITE VARCHAR(10),
            BMBA001 VARCHAR(40),
            BMBA003 VARCHAR(40),
            BMBA004 VARCHAR(40),
            INDEX idx_temp (BMBAENT, BMBASITE, BMBA001, BMBA003, BMBA004)
        )
    ");

    // 分批插入Oracle主键到临时表
    echo "   正在分批获取Oracle主键..." . PHP_EOL;
    $batchSize = 10000;
    $offset = 0;
    $totalInserted = 0;

    while (true) {
        $oracleBatch = DB::connection('oracle')
            ->table('BMBA_T')
            ->where('BMBAENT', 40)
            ->select('BMBAENT', 'BMBASITE', 'BMBA001', 'BMBA003', 'BMBA004')
            ->offset($offset)
            ->limit($batchSize)
            ->get();

        if ($oracleBatch->isEmpty()) {
            break;
        }

        $insertData = [];
        foreach ($oracleBatch as $record) {
            $insertData[] = [
                'BMBAENT' => $record->bmbaent,
                'BMBASITE' => $record->bmbasite,
                'BMBA001' => $record->bmba001,
                'BMBA003' => $record->bmba003,
                'BMBA004' => $record->bmba004,
            ];
        }

        DB::table('temp_oracle_keys')->insert($insertData);
        $totalInserted += count($insertData);

        echo "     已处理: " . number_format($totalInserted) . " 条\r";

        $offset += $batchSize;
    }

    echo PHP_EOL . "   Oracle主键总数: " . number_format($totalInserted) . PHP_EOL;

    // 使用LEFT JOIN找出缺失记录
    $missingRecords = DB::select("
        SELECT o.BMBAENT, o.BMBASITE, o.BMBA001, o.BMBA003, o.BMBA004
        FROM temp_oracle_keys o
        LEFT JOIN BMBA_T m ON
            o.BMBAENT = m.BMBAENT AND
            o.BMBASITE = m.BMBASITE AND
            o.BMBA001 = m.BMBA001 AND
            o.BMBA003 = m.BMBA003 AND
            o.BMBA004 = m.BMBA004
        WHERE m.BMBAENT IS NULL
        LIMIT 1000
    ");

    echo "   实际缺失记录数（前1000条）: " . count($missingRecords) . PHP_EOL . PHP_EOL;
    
    // 3. 分析缺失记录的模式
    echo "3. 分析缺失记录的模式:" . PHP_EOL;

    if (count($missingRecords) > 0) {
        echo "   分析缺失记录模式..." . PHP_EOL;

        $patterns = [
            'BMBASITE' => [],
            'BMBA001_prefix' => [],
            'BMBA003_prefix' => [],
            'BMBA004_pattern' => []
        ];

        foreach ($missingRecords as $record) {
            $bmbasite = $record->BMBASITE;
            $bmba001 = $record->BMBA001;
            $bmba003 = $record->BMBA003;
            $bmba004 = $record->BMBA004;

            // 统计BMBASITE分布
            $patterns['BMBASITE'][$bmbasite] = ($patterns['BMBASITE'][$bmbasite] ?? 0) + 1;

            // 统计BMBA001前缀
            $prefix001 = substr($bmba001, 0, 4);
            $patterns['BMBA001_prefix'][$prefix001] = ($patterns['BMBA001_prefix'][$prefix001] ?? 0) + 1;

            // 统计BMBA003前缀
            $prefix003 = substr($bmba003, 0, 4);
            $patterns['BMBA003_prefix'][$prefix003] = ($patterns['BMBA003_prefix'][$prefix003] ?? 0) + 1;

            // 统计BMBA004模式
            $pattern004 = strlen(trim($bmba004)) == 0 ? 'EMPTY' : 'NOT_EMPTY';
            $patterns['BMBA004_pattern'][$pattern004] = ($patterns['BMBA004_pattern'][$pattern004] ?? 0) + 1;
        }

        echo "   BMBASITE分布:" . PHP_EOL;
        arsort($patterns['BMBASITE']);
        foreach (array_slice($patterns['BMBASITE'], 0, 5, true) as $site => $count) {
            echo "     {$site}: {$count} 条" . PHP_EOL;
        }

        echo "   BMBA001前缀分布:" . PHP_EOL;
        arsort($patterns['BMBA001_prefix']);
        foreach (array_slice($patterns['BMBA001_prefix'], 0, 5, true) as $prefix => $count) {
            echo "     {$prefix}*: {$count} 条" . PHP_EOL;
        }

        echo "   BMBA003前缀分布:" . PHP_EOL;
        arsort($patterns['BMBA003_prefix']);
        foreach (array_slice($patterns['BMBA003_prefix'], 0, 5, true) as $prefix => $count) {
            echo "     {$prefix}*: {$count} 条" . PHP_EOL;
        }

        echo "   BMBA004模式:" . PHP_EOL;
        foreach ($patterns['BMBA004_pattern'] as $pattern => $count) {
            echo "     {$pattern}: {$count} 条" . PHP_EOL;
        }

        echo PHP_EOL;
        
        // 4. 尝试手动插入缺失记录样本
        echo "4. 尝试手动插入缺失记录样本:" . PHP_EOL;

        $testRecords = array_slice($missingRecords, 0, 5);
        $insertSuccess = 0;
        $insertFailed = 0;
        $failureReasons = [];

        foreach ($testRecords as $record) {
            $keyStr = "{$record->BMBAENT}|{$record->BMBASITE}|{$record->BMBA001}|{$record->BMBA003}|{$record->BMBA004}";
            echo "   测试插入: " . $keyStr . PHP_EOL;

            try {
                // 从Oracle获取完整记录
                $oracleRecord = DB::connection('oracle')
                    ->table('BMBA_T')
                    ->where('BMBAENT', $record->BMBAENT)
                    ->where('BMBASITE', $record->BMBASITE)
                    ->where('BMBA001', $record->BMBA001)
                    ->where('BMBA003', $record->BMBA003)
                    ->where('BMBA004', $record->BMBA004)
                    ->first();

                if ($oracleRecord) {
                    $recordArray = (array) $oracleRecord;

                    // 移除Oracle特有字段
                    unset($recordArray['rnum']);
                    unset($recordArray['RNUM']);
                    unset($recordArray['rn']);
                    unset($recordArray['RN']);

                    // 转换为大写
                    $upperCaseRecord = [];
                    foreach ($recordArray as $key => $value) {
                        $upperCaseRecord[strtoupper($key)] = $value;
                    }

                    // 尝试插入
                    DB::table('BMBA_T')->insert($upperCaseRecord);
                    $insertSuccess++;
                    echo "     ✅ 插入成功" . PHP_EOL;

                    // 立即删除以免影响后续测试
                    DB::table('BMBA_T')
                        ->where('BMBAENT', $record->BMBAENT)
                        ->where('BMBASITE', $record->BMBASITE)
                        ->where('BMBA001', $record->BMBA001)
                        ->where('BMBA003', $record->BMBA003)
                        ->where('BMBA004', $record->BMBA004)
                        ->delete();

                } else {
                    echo "     ❌ Oracle中未找到记录" . PHP_EOL;
                    $insertFailed++;
                }

            } catch (Exception $e) {
                $insertFailed++;
                $errorMsg = $e->getMessage();
                echo "     ❌ 插入失败: " . $errorMsg . PHP_EOL;

                // 分析错误类型
                if (strpos($errorMsg, 'Duplicate entry') !== false) {
                    $failureReasons['duplicate'][] = $keyStr;
                } elseif (strpos($errorMsg, 'Data too long') !== false) {
                    $failureReasons['data_too_long'][] = $keyStr;
                } elseif (strpos($errorMsg, 'Incorrect') !== false) {
                    $failureReasons['data_type'][] = $keyStr;
                } elseif (strpos($errorMsg, 'cannot be null') !== false) {
                    $failureReasons['null_constraint'][] = $keyStr;
                } else {
                    $failureReasons['other'][] = $keyStr;
                }
            }
        }

        echo PHP_EOL . "   插入测试结果:" . PHP_EOL;
        echo "     成功: {$insertSuccess}/5" . PHP_EOL;
        echo "     失败: {$insertFailed}/5" . PHP_EOL;

        if (!empty($failureReasons)) {
            echo "   失败原因分析:" . PHP_EOL;
            foreach ($failureReasons as $reason => $keys) {
                echo "     {$reason}: " . count($keys) . " 条" . PHP_EOL;
            }
        }
    }
    
    echo PHP_EOL;
    
    // 5. 检查MySQL表结构和约束
    echo "5. 检查MySQL表结构和约束:" . PHP_EOL;
    
    $tableInfo = DB::select("SHOW CREATE TABLE BMBA_T");
    if (!empty($tableInfo)) {
        $createSql = $tableInfo[0]->{'Create Table'};
        
        // 检查主键约束
        if (preg_match('/PRIMARY KEY \((.*?)\)/', $createSql, $matches)) {
            echo "   主键约束: " . $matches[1] . PHP_EOL;
        }
        
        // 检查唯一约束
        if (preg_match_all('/UNIQUE KEY.*?\((.*?)\)/', $createSql, $matches)) {
            echo "   唯一约束: " . implode(', ', $matches[1]) . PHP_EOL;
        }
        
        // 检查字符集
        if (preg_match('/CHARSET=(\w+)/', $createSql, $matches)) {
            echo "   字符集: " . $matches[1] . PHP_EOL;
        }
    }
    
    echo PHP_EOL;
    
    // 6. 总结和建议
    echo "6. 问题总结和建议:" . PHP_EOL;
    echo "   📊 确认缺失记录数: " . number_format(count($missingRecords)) . " 条（样本）" . PHP_EOL;
    
    if ($insertSuccess > 0) {
        echo "   ✅ 手动插入测试成功，说明数据本身没有问题" . PHP_EOL;
        echo "   🔍 可能原因: 同步过程中的批次失败或事务回滚" . PHP_EOL;
        echo "   💡 建议: 重新执行全量同步应该能解决问题" . PHP_EOL;
    } else {
        echo "   ❌ 手动插入测试失败，存在数据质量问题" . PHP_EOL;
        echo "   🔍 需要进一步分析失败原因" . PHP_EOL;
    }

} catch (Exception $e) {
    echo "❌ 调查过程中发生错误: " . $e->getMessage() . PHP_EOL;
    echo "错误详情: " . $e->getTraceAsString() . PHP_EOL;
}

echo PHP_EOL . "=== 调查完成 ===" . PHP_EOL;
